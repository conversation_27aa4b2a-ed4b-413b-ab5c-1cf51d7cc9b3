-- Store Bot Database Setup - Complete Edition
-- این فایل همه تیبل‌های مورد نیاز را چک می‌کند و در صورت عدم وجود می‌سازد
-- همچنین ستون‌های جدید را به تیبل‌های موجود اضافه می‌کند
-- تاریخ ایجاد: 2025-07-31
-- نسخه: 2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- ========================================
-- بخش اول: ایجاد تیبل‌های اصلی
-- ========================================

--
-- Table structure for table `apis`
-- جدول API ها
--
CREATE TABLE IF NOT EXISTS `apis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `token` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `site` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int(255) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `autoorders`
-- جدول سفارشات خودکار
--
CREATE TABLE IF NOT EXISTS `autoorders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(1024) DEFAULT NULL,
  `pid` int(255) DEFAULT NULL,
  `cid` varchar(1024) DEFAULT NULL,
  `quantity` int(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `category`
-- جدول دسته‌بندی‌ها
--
CREATE TABLE IF NOT EXISTS `category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `parent` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
  `api` int(7) DEFAULT NULL,
  `creator` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sort` int(255) NOT NULL DEFAULT 1000,
  `checked` int(10) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `manual`
-- جدول سفارشات دستی
--
CREATE TABLE IF NOT EXISTS `manual` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `pid` int(255) NOT NULL,
  `cost` float NOT NULL,
  `time` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `orders`
-- جدول سفارشات
--
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `quantity` int(255) NOT NULL,
  `link` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `cost` int(255) NOT NULL,
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `track` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int(255) NOT NULL DEFAULT 0,
  `agency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `start_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
  `remains` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
  `api` int(5) NOT NULL DEFAULT 0,
  `msgid` int(255) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `payment`
-- جدول پرداخت‌ها
--
CREATE TABLE IF NOT EXISTS `payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `cost` int(255) NOT NULL,
  `time` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `agency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_type` VARCHAR(50) DEFAULT 'manual',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `product`
-- جدول محصولات
--
CREATE TABLE IF NOT EXISTS `product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `price` float DEFAULT NULL,
  `old_price` float DEFAULT NULL,
  `category` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `min` int(255) DEFAULT NULL,
  `max` int(255) DEFAULT NULL,
  `des` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `api` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `code` int(255) DEFAULT NULL,
  `creator` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sort` int(255) NOT NULL DEFAULT 1000,
  `status` int(255) NOT NULL DEFAULT 1,
  `checked` int(5) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `sendall`
-- جدول ارسال پیام همگانی
--
CREATE TABLE IF NOT EXISTS `sendall` (
  `id` int(255) NOT NULL AUTO_INCREMENT,
  `step` varchar(20) DEFAULT NULL,
  `text` text DEFAULT NULL,
  `chat` varchar(100) DEFAULT NULL,
  `user` int(11) DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table `tusers`
-- جدول کاربران تلگرام
--
CREATE TABLE IF NOT EXISTS `tusers` (
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `first_name` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `last_name` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `username` varchar(1023) DEFAULT NULL,
  `step` varchar(16) DEFAULT NULL,
  `invite` int(127) NOT NULL DEFAULT 0,
  `invited_by` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `wallet` int(127) NOT NULL DEFAULT 0,
  `invite_earn` int(127) NOT NULL DEFAULT 0,
  `last_order` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_number` bigint(20) DEFAULT NULL,
  `number` int(30) NOT NULL DEFAULT 0,
  `send` int(255) NOT NULL DEFAULT 0,
  `status` int(10) NOT NULL DEFAULT 0,
  `phone` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_sms` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `user`
-- جدول کاربران اصلی
--
CREATE TABLE IF NOT EXISTS `user` (
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(1024) DEFAULT NULL,
  `wallet` int(255) NOT NULL DEFAULT 0,
  `step` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'home',
  `datash` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `datam` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `datap` VARCHAR(255) DEFAULT NULL,
  `agency` int(255) NOT NULL DEFAULT 0,
  `earn` int(255) NOT NULL DEFAULT 0,
  `agencyTotal` int(255) NOT NULL DEFAULT 0,
  `earnTotal` int(255) NOT NULL DEFAULT 0,
  `invite` int(16) NOT NULL DEFAULT 0,
  `invitedBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `token` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` int(16) NOT NULL DEFAULT 0,
  `phone` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `lastSMS` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Table structure for table `nowpayments`
-- جدول پرداخت‌های NOWPayments
--
CREATE TABLE IF NOT EXISTS `nowpayments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL,
  `payment_id` varchar(255) NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `price_amount` decimal(10,2) NOT NULL,
  `price_currency` varchar(10) NOT NULL DEFAULT 'usd',
  `pay_amount` decimal(20,8) NOT NULL,
  `pay_currency` varchar(20) NOT NULL,
  `pay_address` text NOT NULL,
  `payment_status` varchar(50) NOT NULL DEFAULT 'waiting',
  `actually_paid` decimal(20,8) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payment_id` (`payment_id`),
  KEY `uid` (`uid`),
  KEY `payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- بخش دوم: بروزرسانی تیبل‌های موجود
-- ========================================

-- اضافه کردن ستون payment_type به جدول payment (در صورت عدم وجود)
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'payment'
    AND COLUMN_NAME = 'payment_type'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `payment` ADD COLUMN `payment_type` VARCHAR(50) DEFAULT ''manual'' AFTER `agency`',
    'SELECT "Column payment_type already exists in payment table" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- اضافه کردن ستون datap به جدول user (در صورت عدم وجود)
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'user'
    AND COLUMN_NAME = 'datap'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `user` ADD COLUMN `datap` VARCHAR(255) DEFAULT NULL AFTER `datam`',
    'SELECT "Column datap already exists in user table" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- بخش سوم: داده‌های اولیه
-- ========================================

-- اطمینان از وجود رکورد اولیه در جدول sendall
INSERT IGNORE INTO `sendall` (`id`, `step`, `text`, `chat`, `user`) VALUES
(1, 'none', '', '', 0);

-- ========================================
-- بخش چهارم: نهایی سازی
-- ========================================

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- ========================================
-- پایان فایل
-- ========================================
-- این فایل شامل:
-- ✅ همه تیبل‌های اصلی با CREATE TABLE IF NOT EXISTS
-- ✅ تیبل nowpayments برای پرداخت‌های کریپتو
-- ✅ بروزرسانی تیبل‌های موجود با ستون‌های جدید
-- ✅ چک کردن وجود ستون‌ها قبل از اضافه کردن
-- ✅ داده‌های اولیه مورد نیاز
--
-- نحوه استفاده:
-- 1. این فایل را در phpMyAdmin یا MySQL client اجرا کنید
-- 2. فایل به صورت خودکار تیبل‌های موجود را چک می‌کند
-- 3. تیبل‌های جدید را در صورت نیاز می‌سازد
-- 4. ستون‌های جدید را به تیبل‌های موجود اضافه می‌کند
