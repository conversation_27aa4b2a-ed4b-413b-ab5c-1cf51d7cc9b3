# راهنمای نصب دیتابیس Store Bot

## فایل‌های SQL موجود

### 1. `database_setup.sql` (پیشنهادی ⭐)
این فایل کامل و بهینه شده است که:
- **همه تیبل‌ها را چک می‌کند** و در صورت عدم وجود می‌سازد
- **ستون‌های جدید را بروزرسانی می‌کند** بدون خرابی داده‌های موجود
- **از `CREATE TABLE IF NOT EXISTS` استفاده می‌کند** برای جلوگیری از خطا
- **شامل تیبل NOWPayments** برای پرداخت‌های کریپتو
- **داده‌های اولیه** مورد نیاز را اضافه می‌کند

### 2. `main.sql` (بروزرسانی شده)
فایل اصلی که بروزرسانی شده و شامل:
- همه تیبل‌های اصلی
- تیبل NOWPayments
- بروزرسانی‌های لازم

### 3. `database_nowpayments.sql` (قدیمی)
فایل قدیمی که فقط شامل تیبل NOWPayments بود.

## نحوه استفاده

### روش 1: استفاده از فایل کامل (پیشنهادی)

```sql
-- در phpMyAdmin یا MySQL client:
SOURCE database_setup.sql;
```

یا محتویات فایل `database_setup.sql` را کپی کرده و در phpMyAdmin اجرا کنید.

### روش 2: اجرای دستی

1. ابتدا دیتابیس را ایجاد کنید:
```sql
CREATE DATABASE IF NOT EXISTS your_database_name;
USE your_database_name;
```

2. سپس فایل `database_setup.sql` را اجرا کنید.

## ویژگی‌های فایل جدید

### ✅ ایمن برای اجرای مکرر
- اگر تیبل‌ها وجود داشته باشند، خطا نمی‌دهد
- اگر ستون‌ها وجود داشته باشند، دوباره اضافه نمی‌کند

### ✅ چک کردن خودکار
```sql
-- مثال چک کردن وجود ستون:
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'payment'
    AND COLUMN_NAME = 'payment_type'
);
```

### ✅ تیبل‌های شامل:
- `apis` - مدیریت API ها
- `autoorders` - سفارشات خودکار
- `category` - دسته‌بندی محصولات
- `manual` - سفارشات دستی
- `orders` - سفارشات
- `payment` - پرداخت‌ها (با ستون جدید `payment_type`)
- `product` - محصولات
- `sendall` - ارسال پیام همگانی
- `tusers` - کاربران تلگرام
- `user` - کاربران اصلی (با ستون جدید `datap`)
- `nowpayments` - پرداخت‌های کریپتو

## تغییرات اعمال شده

### بروزرسانی جدول `payment`:
```sql
ALTER TABLE `payment` ADD COLUMN `payment_type` VARCHAR(50) DEFAULT 'manual' AFTER `agency`;
```

### بروزرسانی جدول `user`:
```sql
ALTER TABLE `user` ADD COLUMN `datap` VARCHAR(255) DEFAULT NULL AFTER `datam`;
```

### تیبل جدید `nowpayments`:
- مدیریت پرداخت‌های کریپتو
- ردیابی وضعیت پرداخت
- ذخیره اطلاعات تراکنش

## نکات مهم

1. **قبل از اجرا حتماً بک‌آپ بگیرید** 🔒
2. فایل برای MySQL/MariaDB طراحی شده
3. نیاز به دسترسی CREATE و ALTER دارد
4. در صورت خطا، پیام‌های مفیدی نمایش می‌دهد

## عیب‌یابی

### اگر خطای دسترسی گرفتید:
```sql
GRANT CREATE, ALTER, INSERT, SELECT ON your_database.* TO 'your_user'@'localhost';
```

### اگر خطای charset گرفتید:
```sql
SET NAMES utf8mb4;
```

### چک کردن وضعیت تیبل‌ها:
```sql
SHOW TABLES;
DESCRIBE table_name;
```

## پشتیبانی

در صورت بروز مشکل:
1. پیام خطا را بررسی کنید
2. مطمئن شوید دسترسی‌های لازم را دارید
3. نسخه MySQL/MariaDB را چک کنید (حداقل 5.7)

---
**نکته:** این فایل‌ها برای Store Bot طراحی شده‌اند و شامل همه تیبل‌های مورد نیاز هستند.
